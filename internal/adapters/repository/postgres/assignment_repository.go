package postgres

import (
	"errors"
	"fmt"

	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"
)

type assignmentRepository struct {
	DB *gorm.DB
}

func NewAssignmentRepository(DB *gorm.DB) *assignmentRepository {
	return &assignmentRepository{DB: DB}
}

func (r *assignmentRepository) GetAll() ([]*domain.Assignment, error) {
	var assignments []*domain.Assignment
	if err := r.DB.
		Preload("CreatedBy").
		Preload("AssignmentLead").
		Preload("Adjuster").
		Preload("Adjuster.Carriers").
		Preload("Carriers").
		Preload("CategoryData").
		Preload("CategoryData.Category").
		Preload("PrimaryContact").
		Preload("SecondaryContact").
		Preload("LossLocation").
		Find(&assignments).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to retrieve all assignments from database", err)
	}
	return assignments, nil
}

func (r *assignmentRepository) GetByUserId(userID uint) ([]*domain.Assignment, error) {
	var assignments []*domain.Assignment

	if err := r.DB.
		Preload("CreatedBy").
		Preload("AssignmentLead").
		Preload("Adjuster").
		Preload("Adjuster.Carriers").
		Preload("Carriers").
		Preload("CategoryData").
		Preload("CategoryData.Category").
		Preload("PrimaryContact").
		Preload("SecondaryContact").
		Preload("LossLocation").
		Where("created_by_id = ?", userID).Find(&assignments).Error; err != nil {
		return nil, appErrors.NewInternalError(fmt.Sprintf("failed to retrieve assignments from database with user id: %d", userID), err)
	}

	return assignments, nil
}

func (r *assignmentRepository) GetByID(id uint) (*domain.Assignment, error) {
	var assignment domain.Assignment

	if err := r.DB.
		Preload("CreatedBy").
		Preload("AssignmentLead").
		Preload("Adjuster").
		Preload("Adjuster.Carriers").
		Preload("Carriers").
		Preload("CategoryData").
		Preload("CategoryData.Category").
		Preload("PrimaryContact").
		Preload("SecondaryContact").
		Preload("LossLocation").
		First(&assignment, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErrors.NewNotFoundError(fmt.Sprintf("assignment with ID %d could not be found", id), err)
		}
		return nil, appErrors.NewInternalError(fmt.Sprintf("failed to retrieve assignment %d due to unexpected issue", id), err)
	}

	return &assignment, nil
}

func (r *assignmentRepository) Create(assignment *domain.Assignment) (*domain.Assignment, error) {
	if result := r.DB.Create(assignment); result.Error != nil {
		var pgErr *pgconn.PgError
		if errors.As(result.Error, &pgErr) {
			fmt.Printf("PostgreSQL error code: %s, message: %s\n", pgErr.Code, pgErr.Message)
			if pgErr.Code == "23505" {
				return nil, appErrors.NewConflictError("assignment with this claim number already exists")
			}
			if pgErr.Code == "23503" {
				return nil, appErrors.NewNotFoundError("foreign key constraint violation, please check that all related entities exist before creating an assignment")
			}
		}
	}
	if err := r.DB.
		Preload("CategoryData").
		Preload("CategoryData.Category").
		Preload("Carriers").
		First(&assignment, assignment.ID).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to load created assignment", err)
	}
	return assignment, nil
}

func (r *assignmentRepository) Update(assignment *domain.Assignment) (*domain.Assignment, error) {
	var newCarriers []*domain.Carrier
	var existing domain.Assignment

	if err := r.DB.First(&existing, assignment.ID).Error; err != nil {
		return nil, appErrors.NewNotFoundError("assignment not found")
	}

	if len(assignment.Carriers) > 0 {
		carrierIDs := make([]uint, len(assignment.Carriers))
		for i, carrier := range assignment.Carriers {
			carrierIDs[i] = carrier.ID
		}
		r.DB.Where("id IN ?", carrierIDs).Find(&newCarriers)
	}

	if err := r.DB.
		Where("assignment_id = ?", assignment.ID).
		Delete(&domain.AssignmentCategory{}).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to delete old assignment categories", err)
	}

	for _, category := range assignment.CategoryData {
		category.AssignmentID = assignment.ID
		if err := r.DB.Create(&category).Error; err != nil {
			return nil, appErrors.NewInternalError("failed to insert assignment category", err)
		}
	}

	if err := r.DB.Model(&existing).
		Association("Carriers").
		Replace(&newCarriers); err != nil {
		return nil, appErrors.NewInternalError("failed to update assignment carriers", err)
	}

	if result := r.DB.
		Model(&existing).Updates(assignment); result.Error != nil {
		var pgErr *pgconn.PgError
		if errors.As(result.Error, &pgErr) {
			fmt.Printf("PostgreSQL error code: %s, message: %s\n", pgErr.Code, pgErr.Message)
			if pgErr.Code == "23505" {
				return nil, appErrors.NewConflictError("assignment with this claim number already exists")
			}
			if pgErr.Code == "23503" {
				return nil, appErrors.NewNotFoundError("foreign key constraint violation, please check that all related entities exist before updating an assignment")
			}
		}
		return nil, appErrors.NewInternalError("failed to update assignment", result.Error)
	}

	return assignment, nil
}
