package service

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
)

type assignmentService struct {
	assignmentRepo out.AssignmentRepository
}

func NewAssignmentService(repo out.AssignmentRepository) in.AssignmentService {
	return &assignmentService{assignmentRepo: repo}
}

func (s *assignmentService) GetAll() ([]*domain.Assignment, error) {
	return s.assignmentRepo.GetAll()
}

func (s *assignmentService) GetByUserId(userID uint) ([]*domain.Assignment, error) {
	return s.assignmentRepo.GetByUserId(userID)
}

func (s *assignmentService) GetByID(id uint) (*domain.Assignment, error) {
	return s.assignmentRepo.GetByID(id)
}

func (s *assignmentService) Create(assignment *domain.Assignment) (*domain.Assignment, error) {

	

	return s.assignmentRepo.Create(assignment)
}

func (s *assignmentService) Update(assignment *domain.Assignment) (*domain.Assignment, error) {
	return s.assignmentRepo.Update(assignment)
}
