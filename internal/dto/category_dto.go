package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type CategoryResponse struct {
	ID        uint      `json:"id"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

func FromDomainCategory(category domain.Category) CategoryResponse {
	return CategoryResponse{
		ID:        category.ID,
		Name:      category.Name,
		CreatedAt: category.CreatedAt,
		UpdatedAt: category.UpdatedAt,
	}
}

type CategoryListResponse struct {
	Results []CategoryResponse `json:"results"`
	Total   int                `json:"total"`
	Message string             `json:"message"`
}

func NewCategoryListResponse(categories []*domain.Category, message string) CategoryListResponse {
	categoriesResponse := make([]CategoryResponse, len(categories))
	for i, cat := range categories {
		categoriesResponse[i] = FromDomainCategory(*cat)
	}
	return CategoryListResponse{
		Results: categoriesResponse,
		Total:   len(categories),
		Message: message,
	}
}

type CreateCategoryRequest struct {
	Name string `json:"name" binding:"required"`
}

func ToDomainCategory(r *CreateCategoryRequest) *domain.Category {
	return &domain.Category{
		Name: r.Name,
	}
}
