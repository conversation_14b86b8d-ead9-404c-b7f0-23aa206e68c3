package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type AssignmentCategoryResponse struct {
	AssignmentID    uint             `json:"assignmentId"`
	CategoryID      uint             `json:"categoryId"`
	PresentValue    float64          `json:"presentValue"`
	AsAnalyzedValue float64          `json:"asAnalyzedValue"`
	Category        CategoryResponse `json:"category"`
	CreatedAt       time.Time        `json:"createdAt"`
	UpdatedAt       time.Time        `json:"updatedAt"`
}

func FromDomainAssignmentCategory(assignmentCategory domain.AssignmentCategory) AssignmentCategoryResponse {
	return AssignmentCategoryResponse{
		AssignmentID:    assignmentCategory.AssignmentID,
		CategoryID:      assignmentCategory.CategoryID,
		PresentValue:    assignmentCategory.PresentValue,
		AsAnalyzedValue: assignmentCategory.AsAnalyzedValue,
		Category:        FromDomainCategory(assignmentCategory.Category),
		CreatedAt:       assignmentCategory.CreatedAt,
		UpdatedAt:       assignmentCategory.UpdatedAt,
	}
}

type CreateAssignmentCategoryRequest struct {
	CategoryID      uint     `json:"categoryId" binding:"required"`
	PresentValue    *float64 `json:"presentValue" binding:"required"`
	AsAnalyzedValue *float64 `json:"asAnalyzedValue" binding:"required"`
}

func ToDomainAssignmentCategory(r *CreateAssignmentCategoryRequest) *domain.AssignmentCategory {
	return &domain.AssignmentCategory{
		CategoryID:      r.CategoryID,
		PresentValue:    *r.PresentValue,
		AsAnalyzedValue: *r.AsAnalyzedValue,
	}
}
